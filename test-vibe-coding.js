#!/usr/bin/env node

/**
 * Test script to verify vibe-coding MCP tools are working
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testMCPServer() {
  console.log('🧪 Testing Vibe Coding MCP Server...');

  const serverPath = path.join(__dirname, 'build', 'index.js');

  return new Promise((resolve, reject) => {
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let hasError = false;

    // Send MCP initialization
    const initMessage =
      JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: {
            name: 'test-client',
            version: '1.0.0'
          }
        }
      }) + '\n';

    server.stdin.write(initMessage);

    // Send tools list request
    setTimeout(() => {
      const toolsMessage =
        JSON.stringify({
          jsonrpc: '2.0',
          id: 2,
          method: 'tools/list',
          params: {}
        }) + '\n';

      server.stdin.write(toolsMessage);
    }, 100);

    server.stdout.on('data', (data) => {
      output += data.toString();

      // Check if we received tools list
      if (
        output.includes('"method":"tools/list"') ||
        output.includes('vibe-coding')
      ) {
        console.log('✅ MCP Server responding correctly');

        // Check for our vibe-coding tool
        if (output.includes('vibe-coding')) {
          console.log('✅ vibe-coding tool found in tools list!');
          console.log('🎉 Automated development pipeline is ready!');
        } else {
          console.log('⚠️ vibe-coding tool not found in response');
        }

        server.kill();
        resolve(true);
      }
    });

    server.stderr.on('data', (data) => {
      console.error('Server error:', data.toString());
      hasError = true;
    });

    server.on('close', (code) => {
      if (!hasError && code === 0) {
        console.log('✅ Server closed successfully');
      } else if (!hasError) {
        console.log('✅ Test completed');
      }
      resolve(!hasError);
    });

    // Timeout after 5 seconds
    setTimeout(() => {
      server.kill();
      if (!hasError) {
        console.log('✅ Test completed (timeout)');
        resolve(true);
      } else {
        reject(new Error('Test timeout'));
      }
    }, 5000);
  });
}

async function main() {
  try {
    console.log('🚀 Starting Vibe Coding MCP Server Test...\n');

    const success = await testMCPServer();

    if (success) {
      console.log(
        '\n🎉 SUCCESS! Vibe Coding is ready for automated development!'
      );
      console.log('\n📋 Available Commands:');
      console.log('  • vibe-coding - 一键式自动化开发流程');
      console.log('  • spec-generation - 规格生成代理');
      console.log('  • spec-executor - 代码实现代理');
      console.log('  • spec-validation - 质量验证代理');
      console.log('  • spec-testing - 测试生成代理');
      console.log('  • vibe-coding-status - 工作流状态查询');

      console.log('\n🚀 Try it now:');
      console.log('  vibe-coding "开发用户认证管理系统"');
      console.log('\n☕ Then grab a coffee and watch the AI expert team work!');
    } else {
      console.log('\n❌ Test failed. Please check the server configuration.');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n❌ Test error:', error.message);
    process.exit(1);
  }
}

main();

/**
 * Sub-Agents 工作流程工具
 * v1.4.0 - Sub-Agents 革命
 */

import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { ProfessionalWorkflow, ProfessionalWorkflowContext } from "../agents/workflows/professional-workflow.js";
import { SubAgentsConfigManager, WorkflowConfig } from "../utils/sub-agents-config.js";
import { AgentContext, AgentType, QualityGate } from "../agents/types.js";
import { ROOT_PATH_DESC } from "../utils/const.js";
import type { WorkflowConfig } from "../types.js";

/**
 * 注册 Sub-Agents 工具
 */
export function registerSubAgentsTools(server: McpServer): void {
  // console.log(`🤖 Registering Sub-Agents tools...`);

  // 主要的 vibe-coding 工具
  server.tool(
    "vibe-coding",
    "🚀 Sub-Agents workflow: One command to complete entire development cycle from requirements to code with 95% quality gates",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      description: z.string().describe("Feature description (e.g., '开发后台管理系统')"),
      qualityThreshold: z.number().min(80).max(100).default(95).describe("Quality threshold percentage"),
      enabledAgents: z.array(z.enum(["spec", "architect", "developer", "quality", "test"])).optional().describe("Enabled agents (default: all)"),
      outputFormat: z.enum(["detailed", "summary", "json"]).default("detailed").describe("Output format"),
    },
    async ({ rootPath, description, qualityThreshold, enabledAgents, outputFormat }) => {
      try {
        console.log(`🚀 Starting Sub-Agents workflow: ${description}`);
        console.log(`📖 Loading configuration from .vibecode/sub-agents-config.json...`);

        // 智能读取配置
        const configManager = SubAgentsConfigManager.getInstance(rootPath);
        const config = configManager.getConfig();

        // 命令行参数覆盖配置文件
        const finalQualityThreshold = qualityThreshold || config.workflow.qualityThreshold;
        const finalOutputFormat = outputFormat || 'detailed';
        const finalEnabledAgents = enabledAgents || Object.keys(config.agents).filter(
          agent => agent !== 'orchestrator' && (config.agents as any)[agent].enabled
        ) as AgentType[];

        console.log(`⚙️  Configuration loaded:`);
        console.log(`   Quality Threshold: ${finalQualityThreshold}%`);
        console.log(`   Output Format: ${finalOutputFormat}`);
        console.log(`   Enabled Agents: ${finalEnabledAgents.join(', ')}`);
        console.log(`   Workflow Timeout: ${config.workflow.timeout / 1000}s`);

        // 创建工作流程上下文
        const workflowId = `workflow-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        const context: AgentContext = {
          projectRoot: rootPath,
          workflowId,
          currentPhase: 'initialization',
          sharedState: {},
          qualityGates: createQualityGatesFromConfig(config.workflow, finalQualityThreshold),
          config: config
        };

        // 创建消息总线
        const messageBus = new MessageBus();

        // 创建并启动代理
        await createAndStartAgents(context, messageBus, finalEnabledAgents);

        // 启动工作流程
        const result = await executeWorkflow(messageBus, {
          title: extractTitle(description),
          description,
          qualityThreshold: finalQualityThreshold,
          config
        }, config.workflow.timeout);

        // 清理资源
        await messageBus.shutdown();

        // 格式化输出
        return formatWorkflowResult(result, outputFormat);

      } catch (error) {
        console.error('❌ Sub-Agents workflow failed:', error);
        return {
          content: [
            {
              type: "text",
              text: `❌ Sub-Agents workflow failed: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // 配置展示工具
  server.tool(
    "show-config",
    "⚙️ Show current Sub-Agents configuration from .vibecode/workflows/config.json",
    {
      rootPath: z.string().describe("Project root directory path").optional()
    },
    async ({ rootPath = "." }) => {
      try {
        console.log('📖 Loading Sub-Agents configuration...\n');

        const configManager = SubAgentsConfigManager.getInstance(rootPath);
        const config = configManager.getConfig();

        console.log('⚙️ Sub-Agents Configuration:');
        console.log('='.repeat(50));

        console.log(`📊 Quality Configuration:`);
        console.log(`   Overall Threshold: ${config.workflow.qualityThreshold}%`);
        console.log(`   Phase Thresholds:`);
        Object.entries(config.workflow.phaseThresholds).forEach(([phase, threshold]) => {
          console.log(`     ${phase}: ${threshold}%`);
        });

        console.log(`\n🤖 Agents Configuration:`);
        const enabledAgents = Object.keys(config.agents).filter(
          agent => (config.agents as any)[agent].enabled
        );
        console.log(`   Enabled Agents: ${enabledAgents.join(', ')}`);
        console.log(`   Workflow Max Retries: ${config.workflow.maxRetries}`);
        console.log(`   Workflow Timeout: ${config.workflow.timeout / 1000}s`);

        console.log(`\n🔄 Workflow Configuration:`);
        console.log(`   Parallel Execution: ${config.workflow.parallelExecution ? 'Yes' : 'No'}`);
        console.log(`   Show Progress: ${config.workflow.showProgress ? 'Yes' : 'No'}`);
        console.log(`   Save Results: ${config.workflow.saveResults ? 'Yes' : 'No'}`);
        console.log(`   Version: ${config.version}`);

        return {
          content: [
            {
              type: "text",
              text: `✅ Configuration loaded successfully from ${rootPath}/.vibecode/workflows/config.json`,
            },
          ],
        };

      } catch (error) {
        console.error('❌ Failed to load configuration:', error);
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to load configuration: ${error instanceof Error ? error.message : 'Unknown error'}`,
            },
          ],
        };
      }
    }
  );

  // 工作流程状态查询工具
  server.tool(
    "vibe-status",
    "Get current status of running Sub-Agents workflow",
    {
      workflowId: z.string().describe("Workflow ID to check status"),
    },
    async ({ workflowId }) => {
      // 这里应该从持久化存储中获取状态
      // 目前返回模拟状态
      return {
        content: [
          {
            type: "text",
            text: `📊 Workflow Status: ${workflowId}\n\nStatus: Running\nCurrent Phase: Implementation\nProgress: 60%\nQuality Score: 92%`,
          },
        ],
      };
    }
  );

  // 代理能力查询工具
  server.tool(
    "vibe-agents",
    "List all available Sub-Agents and their capabilities",
    {},
    async () => {
      const capabilities = {
        "🎯 Orchestrator Agent": [
          "Workflow orchestration and coordination",
          "Phase management and progression",
          "Quality gate enforcement",
          "Agent communication coordination"
        ],
        "📋 Spec Agent": [
          "Requirements analysis and specification",
          "User story generation (EARS format)",
          "Acceptance criteria definition",
          "Requirements quality assessment"
        ],
        "🏗️ Architect Agent": [
          "System architecture design",
          "Technical stack selection",
          "API design and data modeling",
          "Architecture quality assessment"
        ],
        "💻 Developer Agent": [
          "Intelligent code generation",
          "Code optimization and refactoring",
          "Implementation best practices",
          "Code quality assurance"
        ],
        "🔍 Quality Agent": [
          "Code quality analysis",
          "Security vulnerability scanning",
          "Performance assessment",
          "Technical debt measurement"
        ],
        "🧪 Test Agent": [
          "Automated test generation",
          "Test coverage analysis",
          "Test quality assessment",
          "Multiple testing frameworks support"
        ]
      };

      const capabilityText = Object.entries(capabilities)
        .map(([agent, caps]) => `${agent}:\n${caps.map(cap => `  • ${cap}`).join('\n')}`)
        .join('\n\n');

      return {
        content: [
          {
            type: "text",
            text: `🤖 Sub-Agents Capabilities\n\n${capabilityText}`,
          },
        ],
      };
    }
  );
}

/**
 * 从配置创建质量门控
 */
function createQualityGatesFromConfig(config: WorkflowConfig, overallThreshold?: number): QualityGate[] {
  const thresholds = config.phaseThresholds;
  const finalOverallThreshold = overallThreshold || config.qualityThreshold;

  return [
    {
      name: "Requirements Quality",
      phase: "requirements",
      threshold: thresholds.requirements || finalOverallThreshold,
      metric: "completeness",
      required: true
    },
    {
      name: "Architecture Quality",
      phase: "architecture",
      threshold: thresholds.architecture || finalOverallThreshold,
      metric: "design_quality",
      required: true
    },
    {
      name: "Code Quality",
      phase: "implementation",
      threshold: thresholds.implementation || finalOverallThreshold,
      metric: "code_quality",
      required: true
    },
    {
      name: "Security Quality",
      phase: "quality",
      threshold: thresholds.quality || Math.max(70, finalOverallThreshold - 20),
      metric: "security_score",
      required: true
    },
    {
      name: "Test Coverage",
      phase: "testing",
      threshold: thresholds.testing || Math.max(70, finalOverallThreshold - 25),
      metric: "test_coverage",
      required: true
    },
    {
      name: "Integration Quality",
      phase: "integration",
      threshold: thresholds.integration || Math.max(70, finalOverallThreshold - 20),
      metric: "integration_quality",
      required: true
    }
  ];
}

/**
 * 创建质量门控（向后兼容）
 */
function createQualityGates(threshold: number): QualityGate[] {
  return [
    {
      name: "Requirements Quality",
      phase: "requirements",
      threshold,
      metric: "completeness",
      required: true
    },
    {
      name: "Architecture Quality",
      phase: "architecture",
      threshold,
      metric: "design_quality",
      required: true
    },
    {
      name: "Code Quality",
      phase: "implementation",
      threshold,
      metric: "code_quality",
      required: true
    },
    {
      name: "Security Quality",
      phase: "quality",
      threshold,
      metric: "security_score",
      required: true
    },
    {
      name: "Test Coverage",
      phase: "testing",
      threshold: Math.max(80, threshold - 10), // 测试覆盖率稍低一些
      metric: "test_coverage",
      required: true
    }
  ];
}

/**
 * 创建并启动代理
 */
async function createAndStartAgents(
  context: AgentContext,
  messageBus: MessageBus,
  enabledAgents?: AgentType[]
): Promise<void> {
  const defaultAgents: AgentType[] = ["spec", "architect", "developer", "quality", "test"];
  const agentsToCreate = enabledAgents || defaultAgents;

  // 总是创建 Orchestrator
  const orchestrator = await AgentFactory.createAgent("orchestrator", context);
  messageBus.registerAgent(orchestrator);
  await orchestrator.start();

  // 创建其他代理
  for (const agentType of agentsToCreate) {
    try {
      const agent = await AgentFactory.createAgent(agentType, context);
      messageBus.registerAgent(agent);
      await agent.start();
    } catch (error) {
      console.error(`❌ Failed to create ${agentType} agent:`, error);
    }
  }
}

/**
 * 执行工作流程
 */
async function executeWorkflow(messageBus: MessageBus, workflowData: any, timeoutMs: number = 300000): Promise<WorkflowResult> {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('Workflow timeout'));
    }, timeoutMs);

    // 监听工作流程完成
    messageBus.on('message', (message) => {
      if (message.type === 'notification' && message.payload.message === 'Workflow completed successfully') {
        clearTimeout(timeout);
        resolve(message.payload.result);
      }
    });

    // 启动工作流程
    messageBus.sendMessage({
      id: `start-${Date.now()}`,
      type: 'request',
      from: 'user',
      to: 'orchestrator',
      payload: {
        action: 'start-workflow',
        data: workflowData
      }
    }).catch(reject);
  });
}

/**
 * 提取标题
 */
function extractTitle(description: string): string {
  // 简单的标题提取逻辑
  if (description.includes('开发')) {
    return description.replace('开发', '').trim();
  }
  return description.substring(0, 50);
}

/**
 * 格式化工作流程结果
 */
function formatWorkflowResult(result: WorkflowResult, format: string): any {
  switch (format) {
    case 'json':
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(result, null, 2),
          },
        ],
      };

    case 'summary':
      return {
        content: [
          {
            type: "text",
            text: `🎉 Workflow Completed Successfully!

📊 **Quality Report**:
- Overall Score: ${result.qualityReport.overallScore}%
- Quality Gates Passed: ${result.qualityReport.gatesPassed}/${result.qualityReport.gatesTotal}
- Duration: ${Math.round(result.duration / 1000)}s

✅ **Phases Completed**: ${result.phases.length}
🎯 **Success Rate**: ${(result.phases.filter(p => p.success).length / result.phases.length * 100).toFixed(1)}%`,
          },
        ],
      };

    default: // detailed
      const phaseDetails = result.phases.map(phase =>
        `${phase.success ? '✅' : '❌'} **${phase.phase}** (${phase.qualityScore}%) - ${Math.round(phase.duration / 1000)}s`
      ).join('\n');

      return {
        content: [
          {
            type: "text",
            text: `🎉 **Sub-Agents Workflow Completed Successfully!**

## 📊 Quality Report
- **Overall Score**: ${result.qualityReport.overallScore}%
- **Quality Gates**: ${result.qualityReport.gatesPassed}/${result.qualityReport.gatesTotal} passed
- **Total Duration**: ${Math.round(result.duration / 1000)} seconds

## 🔄 Phase Results
${phaseDetails}

## 📋 Generated Artifacts
- Requirements Specification ✅
- System Architecture Design ✅
- Implementation Code ✅
- Quality Analysis Report ✅
- Test Suite ✅

## 🎯 Next Steps
Your feature is ready for deployment! All quality gates have been passed and the code meets enterprise standards.`,
          },
        ],
      };
  }
}
